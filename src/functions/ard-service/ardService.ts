import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
} from "@functions/ard-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

// Types
interface SQSRecord {
  body: string;
  messageId: string;
  receiptHandle?: string;
}

interface ApplicationDetails {
  requestId: string;
  applicationFilledBy?: string;
  APIKEY: string;
  sectionLabel: string;
  email: string;
  applicationId: string;
  applicationStatus?: string;
  submittedToSf?: boolean;
  privacyPolicyConsent?: boolean;
  haveCriminalConvictions?: string;
  requireStudentVisa?: string;
  passedEnglishTest?: string;
  passportNumber?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  passportIssuingCountryDisplayName?: string;
  isEnglishFirstLanguage?: string;
  englishCertificationNo?: string;
  englishQualificationType?: string;
  englishTestScore?: string;
  englishMediumYearsDisplayName?: string;
  qualification?: any[];
  priorEmployments?: any[];
  educationHistory?: any[];
  workHistory?: any[];
  documents?: any[];
  identityDocumentObject?: any[];
  languageProficiency?: any[];
  [key: string]: any;
}

interface RequestData {
  sectionLabel: string;
  email: string;
  applicationId: string;
  requestId: string;
  messageId: string;
  [key: string]: any;
}

interface ReferenceMappedDetails {
  appDetails: ApplicationDetails;
  institutionData?: {
    documents: any[];
    educationHistory: any[];
  };
  workHisData?: {
    documents: any[];
    workHistory: any[];
  };
}

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();
const BRAND = "ARD";

// Re-export postDataSf for use in helper functions
export { postDataSf };

/**
 * Transforms boolean fields from string values to boolean values
 */
function transformBooleanFields(applicationDetails: ApplicationDetails): void {
  // Invert privacy policy consent (seems to be inverted logic in original)
  if (applicationDetails?.privacyPolicyConsent) {
    applicationDetails.privacyPolicyConsent =
      !applicationDetails.privacyPolicyConsent;
  }

  // Transform criminal convictions from Yes/No to boolean
  applicationDetails.criminalConvictions =
    applicationDetails?.haveCriminalConvictions === "Yes";

  // Transform visa requirement from Yes/No to boolean
  applicationDetails.visa = applicationDetails?.requireStudentVisa === "Yes";

  applicationDetails.personalStatement =
    applicationDetails?.hasPersonalStatement === "Yes";
}

/**
 * Creates identity document object from passport information
 */
function createIdentityDocumentObject(
  applicationDetails: ApplicationDetails
): void {
  applicationDetails.identityDocumentObject = [
    {
      identityNumber: applicationDetails?.passportNumber,
      identityIssueDate: applicationDetails?.passportIssueDate,
      identityExpiryDate: applicationDetails?.passportExpiryDate,
      identityIssuingCountry:
        applicationDetails?.passportIssuingCountryDisplayName,
      identityType: "Passport",
    },
  ];
}

/**
 * Checks if a work history entry has meaningful employment data beyond just document uploads
 */
function hasValidWorkHistoryData(workEntry: any): boolean {
  if (!workEntry || typeof workEntry !== "object") {
    return false;
  }

  // Define the meaningful work history fields (excluding document-only fields like 'cv')
  const meaningfulFields = [
    "company",
    "department",
    "industry",
    "duties",
    "employmentStatus",
    "startDate",
    "endDate",
  ];

  // Check if any meaningful field has a non-empty value
  return meaningfulFields.some((field) => {
    const value = workEntry[field];
    return value !== null && value !== undefined && value !== "";
  });
}

/**
 * Checks if an education history entry has meaningful educational data beyond just document uploads
 */
function hasValidEducationHistoryData(educationEntry: any): boolean {
  if (!educationEntry || typeof educationEntry !== "object") {
    return false;
  }

  // Define the meaningful education history fields (excluding document-only fields like 'academicCertificate')
  const meaningfulFields = [
    "institutionCountryDisplayName",
    "institutionNameDisplayName",
    "qualificationName",
    "subject",
    "isQualificationCompleted",
    "grade",
    "monthCompleted",
    "expectedMonthCompleted",
  ];

  // Check if any meaningful field has a non-empty value
  return meaningfulFields.some((field) => {
    const value = educationEntry[field];
    return value !== null && value !== undefined && value !== "";
  });
}

/**
 * Creates language proficiency object from English test information
 */
function createLanguageProficiencyObject(
  applicationDetails: ApplicationDetails
): void {
  if (applicationDetails.passedEnglishTest === "Yes") {
    applicationDetails.languageProficiency = [
      {
        isEnglishFirstLanguage:
            applicationDetails.isEnglishFirstLanguage === "Yes"
              ? "Native English Speaker" : null,
        englishQualificationType: applicationDetails?.englishQualificationType,
        englishCertificationNo: applicationDetails?.englishCertificationNo,
        englishTestScore: applicationDetails?.englishTestScore,
        englishMediumYearsDisplayName: applicationDetails?.englishMediumYearsDisplayName,
      },
    ];
  }
}

/**
 * Creates visa application object from visa requirement information
 */
function createVisaApplicationObject(
  applicationDetails: ApplicationDetails
): void {
  applicationDetails.visaApplication = [
    {
      visaRequired: applicationDetails?.requireStudentVisa === "Yes" ? true : false,
    },
  ];
}

/**
 * Maps qualification and employment fields to their target field names
 */
function mapQualificationAndEmploymentFields(
  applicationDetails: ApplicationDetails
): void {
  const fieldMappings = {
    qualification: "educationHistory",
    priorEmployments: "workHistory",
    refereeDetails: "connections",
  };

  for (const [sourceKey, targetKey] of Object.entries(fieldMappings)) {
    if (applicationDetails.hasOwnProperty(sourceKey)) {
      applicationDetails[targetKey] = applicationDetails[sourceKey];
    }
  }

  // Ensure educationHistory exists as an array
  if (!applicationDetails.educationHistory) {
    applicationDetails.educationHistory = [];
  }
  if (!applicationDetails.refereeDetails) {
    applicationDetails.refereeDetails = [];
  }
}

/**
 * Processes misc field
 */
function createMiscFieldObject(applicationDetails: ApplicationDetails) {
  applicationDetails.miscDetails = JSON.stringify({
    Other__MiddleName__c: applicationDetails?.otherMiddleName,
    Previous_LastName__c: applicationDetails?.previousLastName,
    Previously_AppliedOrStudiedInArden__c:
      applicationDetails?.previouslyAppliedOrStudiedInArden,
    Student_Code__c: applicationDetails?.studentCode,
    // contact details section
    Property_Name__c: applicationDetails?.propertyName,
    Is_CorrespondanceAddressDiffer__c:
      applicationDetails?.isCorrespondanceAddressDiffer,
    No_AcademicEvidence__c: applicationDetails?.noAcademicEvidence,
    // Qualifications - APL Section
    Has_RPL__c: applicationDetails?.hasRPL,
    Prior_LearningDetails__c: applicationDetails?.priorLearningDetails,
    Is_EnglishFirstLanguage: applicationDetails?.isEnglishFirstLanguage,
    First_OfficialLanguage__c: applicationDetails?.firstOfficialLanguage,
    Studied_InEnglishMedium__c: applicationDetails?.studiedInEnglishMedium,
    Passed_EnglishTest__c: applicationDetails?.passedEnglishTest,
    // Experience Section
    Other_WorkExperience__c: applicationDetails?.otherWorkExperience,
    Other_WorkExperienceDetails__c:
      applicationDetails?.otherWorkExperienceDetails,
    //
    Personal_Statement__c: applicationDetails?.personalStatement,
    // Visa Section
    Studied_In_UKDegreeLevel__c: applicationDetails?.studiedInUKDegreeLevel,
    Uk_HighestStudyDetails__c: applicationDetails?.ukHighestStudyDetails,
    Uk_StudyDuration__c: applicationDetails?.ukStudyDuration,
    Uk_StudyVisaTypes__c: applicationDetails?.ukStudyVisaTypes,

    // Referee
    Provide_ReferencesNow__c: applicationDetails?.provideReferencesNow,
    Refree_Title__c: applicationDetails?.refereeDetails,

    // Funding Section
    Funding_Sources__c: applicationDetails?.fundingSources,

    // Submission
    MarketingData__c: applicationDetails?.marketingData,
  });
}

/**
 * Processes submitted applications by creating required objects and mappings
 */
async function processSubmittedApplication(
  applicationDetails: ApplicationDetails
): Promise<ReferenceMappedDetails | undefined> {
  const isSubmittedApplication =
    applicationDetails?.applicationStatus === "submitted" &&
    !applicationDetails?.submittedToSf;

  if (!isSubmittedApplication) {
    return undefined;
  }

  try {
    createIdentityDocumentObject(applicationDetails);
    createLanguageProficiencyObject(applicationDetails);
    createVisaApplicationObject(applicationDetails);
    mapQualificationAndEmploymentFields(applicationDetails);
    createMiscFieldObject(applicationDetails);

    return opportunityFileReferenceMapping(applicationDetails);
  } catch (error) {
    console.log("Error in mapping application details", error);
    throw error;
  }
}

/**
 * Processes and sends data to Salesforce based on whether reference mapped details exist
 */
async function processSalesforceData(
  baseRequest: RequestData,
  applicationDetails: ApplicationDetails,
  referenceMappedDetails: ReferenceMappedDetails | undefined,
  apiKey: string,
  filledBy: string
): Promise<void> {
  const hasReferenceData =
    referenceMappedDetails?.institutionData ||
    referenceMappedDetails?.workHisData;

  if (hasReferenceData) {
    console.log("Processing multiple Salesforce objects");
    await processMultipleSalesforceObjects(
      baseRequest,
      applicationDetails,
      referenceMappedDetails,
      apiKey,
      filledBy
    );
  } else {
    await processSingleSalesforceObject(
      baseRequest,
      applicationDetails,
      apiKey,
      filledBy
    );
  }
}

/**
 * Processes multiple Salesforce objects (institution, work history, and main application)
 */
async function processMultipleSalesforceObjects(
  baseRequest: RequestData,
  applicationDetails: ApplicationDetails,
  referenceMappedDetails: ReferenceMappedDetails,
  apiKey: string,
  filledBy: string
): Promise<void> {
  const mappedData = await Promise.all([
    referenceMappedDetails?.institutionData
      ? mapSalesforceObject(referenceMappedDetails.institutionData, filledBy)
      : null,
    referenceMappedDetails?.workHisData
      ? mapSalesforceObject(referenceMappedDetails.workHisData, filledBy)
      : null,
    mapSalesforceObject(applicationDetails, filledBy),
  ]);

  const [mappedInstitutionData, mappedWorkHisData, mappedApplicationData] =
    mappedData;

  // Send institution and work history data in parallel
  await Promise.all([
    mappedInstitutionData &&
      postDataSf(
        { ...baseRequest, ...mappedInstitutionData },
        apiKey,
        filledBy
      ),
    mappedWorkHisData &&
      postDataSf({ ...baseRequest, ...mappedWorkHisData }, apiKey, filledBy),
  ]);

  // Send main application data
  await postDataSf(
    { ...baseRequest, ...mappedApplicationData },
    apiKey,
    filledBy
  );
}

/**
 * Processes a single Salesforce object (main application only)
 */
async function processSingleSalesforceObject(
  baseRequest: RequestData,
  applicationDetails: ApplicationDetails,
  apiKey: string,
  filledBy: string
): Promise<void> {
  const mappedApplicationData = await mapSalesforceObject(
    applicationDetails,
    filledBy
  );
  console.log("Mapped application data ->", mappedApplicationData);
  const requestData = { ...baseRequest, ...mappedApplicationData };
  await postDataSf(requestData, apiKey, filledBy);
}

export const handleArdSfSaveOrUpdateRequests = async (
  event: SQSRecord[]
): Promise<void> => {
  console.log(JSON.stringify(event));

  for (const record of event) {
    let applicationDetails: ApplicationDetails = JSON.parse(record.body);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const apiKey = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();

    // Transform boolean fields
    transformBooleanFields(applicationDetails);

    try {
      // Process submitted applications
      const referenceMappedDetails = await processSubmittedApplication(
        applicationDetails
      );

      if (referenceMappedDetails) {
        applicationDetails = referenceMappedDetails.appDetails;
      }

      // Create base request object
      const baseRequest: RequestData = {
        sectionLabel: applicationDetails.sectionLabel,
        email: applicationDetails.email,
        applicationId: applicationDetails.applicationId,
        requestId: requestId,
        messageId: record.messageId,
      };

      // Process and send data to Salesforce
      await processSalesforceData(
        baseRequest,
        applicationDetails,
        referenceMappedDetails,
        apiKey,
        filledBy
      );

      // Log successful operation
      await logSuccessfulOperation(requestId, currentUTC, applicationDetails);
    } catch (error) {
      // Log error
      await logErrorOperation(requestId, currentUTC, applicationDetails, error);
    }
  }
};

/**
 * Logs successful Salesforce operation
 */
async function logSuccessfulOperation(
  requestId: string,
  currentUTC: string,
  applicationDetails: ApplicationDetails
): Promise<void> {
  await cloudWatchLoggerService.log(
    requestId,
    currentUTC,
    loggerEnum.Component.OAP_HANDLERS,
    loggerEnum.Component.OAP_BACKEND,
    loggerEnum.Component.GUS_EIP_SERVICE,
    loggerEnum.Event.OPERATION_COMPLETED,
    loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
    applicationDetails,
    applicationDetails,
    "Successfully saved to salesforce",
    BRAND,
    applicationDetails.email,
    `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
    "Application_Form_Id__c",
    applicationDetails.applicationId,
    "Opportunity_Application_Account",
    applicationDetails.applicationId
  );
}

/**
 * Logs error operation
 */
async function logErrorOperation(
  requestId: string,
  currentUTC: string,
  applicationDetails: ApplicationDetails,
  error: any
): Promise<void> {
  const errorMessage = error.errorDetails
    ? typeof error.errorDetails === "string"
      ? error.errorDetails
      : JSON.stringify(error.errorDetails)
    : JSON.stringify(error);

  await cloudWatchLoggerService.error(
    requestId,
    currentUTC,
    loggerEnum.Component.OAP_HANDLERS,
    loggerEnum.Component.OAP_BACKEND,
    loggerEnum.Component.GUS_EIP_SERVICE,
    loggerEnum.Event.SAVED_TO_SALESFORCE,
    loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
    applicationDetails,
    applicationDetails,
    errorMessage,
    BRAND,
    applicationDetails.email,
    `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
    "Application_Form_Id__c",
    applicationDetails.applicationId,
    "Opportunity_Application_Account",
    applicationDetails.applicationId
  );
}

function opportunityFileReferenceMapping(
  appDetails: ApplicationDetails
): ReferenceMappedDetails {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map<string, any>();
    appDetails.documents.forEach((doc: any) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (
      documentIds: string[],
      referenceKey: string,
      referenceValue: string,
      key?: string
    ): void => {
      if (Array.isArray(documentIds)) {
        documentIds.forEach((docId: string) => {
          const matchingDocument = documentMap.get(docId);
          if (matchingDocument) {
            matchingDocument[referenceKey] = referenceValue;
          }
          if (key === "passportDocument" && matchingDocument) {
            matchingDocument.isIdentityInformationAccurate =
              appDetails?.isIdentityInformationAccurate === "Yes";
          }
        });
      }
    };
    let institutionData = {
      educationHistory: [],
      documents: [],
    };

    let workHisData = {
      workHistory: [],
      documents: [],
    };
    console.log("appDetails.educationHistory", appDetails.educationHistory);
    if (Array.isArray(appDetails.educationHistory)) {
      // Filter education history entries that have meaningful educational data
      const validEducationEntries = appDetails.educationHistory.filter(
        hasValidEducationHistoryData
      );

      if (validEducationEntries.length > 0) {
        validEducationEntries.forEach((institution, index) => {
          const institutionOrder = index + 1;
          const academicCertificate = institution.academicCertificate || [];
          console.log("academicCertificate", academicCertificate);

          const documentIds = [
            ...academicCertificate.map((doc: any) => doc.documentId),
          ];

          addReferences(
            documentIds,
            "eduReference",
            `@{EducationHistoryRecord__c_${institutionOrder}.id}`
          );
          institutionData.educationHistory.push(institution);
          institutionData.documents.push(
            ...documentIds
              .map((docId) => documentMap.get(docId))
              .filter(Boolean)
          );
        });

        appDetails.educationHistory = [];
        appDetails.documents = appDetails.documents.filter(
          (doc) =>
            !institutionData.documents.some(
              (institutionDoc) => institutionDoc.documentId === doc.documentId
            )
        );
      } else {
        console.log(
          "No valid education history data found - only academic certificates without educational details. Skipping education history mapping."
        );
      }
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }
    if (Array.isArray(appDetails.workHistory)) {
      // Filter work history entries that have meaningful employment data
      const validWorkEntries = appDetails.workHistory.filter(
        hasValidWorkHistoryData
      );

      if (validWorkEntries.length > 0) {
        validWorkEntries.forEach((work, index) => {
          const workOrder = index + 1;
          const cv = work.cv || [];

          const documentIds = [...cv.map((doc: any) => doc.documentId)];

          addReferences(
            documentIds,
            "workReference",
            `@{WorkHistoryRecord__c_${workOrder}.id}`
          );
          workHisData.workHistory.push(work);
          workHisData.documents.push(
            ...documentIds
              .map((docId) => documentMap.get(docId))
              .filter(Boolean)
          );
        });

        appDetails.workHistory = [];
        appDetails.documents = appDetails.documents.filter(
          (doc) =>
            !workHisData.documents.some(
              (workCv) => workCv.documentId === doc.documentId
            )
        );
      } else {
        console.log(
          "No valid work history data found - only CV documents without employment details. Skipping work history mapping."
        );
      }
    } else {
      console.error(
        "No work history data found in appDetails. Nothing to process."
      );
    }
    const categories = [
      {
        key: "englishCertificateUpload",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
    ];

    if (Array.isArray(categories)) {
      categories.forEach(({ key, referenceKey, referenceValue }) => {
        if (Array.isArray(appDetails[key])) {
          const documentIds = appDetails[key].map((doc: any) => doc.documentId);
          addReferences(documentIds, referenceKey, referenceValue, key);
        }
      });
    }

    return { appDetails, institutionData, workHisData };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
    workHisData: { documents: [], workHistory: [] },
  };
}

async function mapSalesforceObject(
  applicationDetails: any,
  filledBy: string
): Promise<any> {
  const result: any = {};
  const salesforceConfig = salesforceAgentConfig;
  const salesforceSubObjectsConfig = salesforceAgentSubObjectsConfig;

  for (const object in salesforceConfig) {
    const configValue = salesforceConfig[object];

    if (Array.isArray(configValue)) {
      const sourceKey = configValue[0];
      if (applicationDetails[sourceKey]) {
        for (const record of applicationDetails[sourceKey]) {
          const mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[sourceKey]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      const mappedObject = await mapValues(applicationDetails, configValue);
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source: any, mapping: any): Promise<any> {
  const result: any = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value: any;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      value = handleMultipleKeys(source, sourceKey);
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      value = getNestedValue(source, sourceKey);
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      assignValueToResult(result, targetKey, value);
    }
  }

  return result;
}

/**
 * Handles multiple keys separated by commas
 */
function handleMultipleKeys(source: any, sourceKey: string): string {
  const keys = sourceKey.split(",");
  const values: any[] = [];

  for (const key of keys) {
    const trimmedKey = key.trim();
    let keyValue: any;

    if (trimmedKey.includes(".")) {
      keyValue = getNestedValue(source, trimmedKey);
    } else {
      keyValue = source[trimmedKey];
    }

    if (keyValue !== undefined) {
      values.push(keyValue);
    }
  }

  return values.join(" ");
}

/**
 * Gets nested value from object using dot notation
 */
function getNestedValue(source: any, keyPath: string): any {
  const keys = keyPath.split(".");
  let value = source;

  for (const key of keys) {
    value = value ? value[key] : undefined;
  }

  return value;
}

/**
 * Assigns value to result object, handling both string and array target keys
 */
function assignValueToResult(result: any, targetKey: any, value: any): void {
  if (Array.isArray(targetKey)) {
    // If the targetKey is an array, map each value to the corresponding key
    targetKey.forEach((key: string) => {
      result[key] = value;
    });
  } else {
    // Otherwise, map the value directly
    result[targetKey as string] = value;
  }
}
