export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    workReference: "Related_Work_History__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  identityDocumentObject: {
    passportNumber: "Identity_Document_Number__c",
    passportIssueDate: "Issue_Date__c",
    passportExpiryDate: "Expiry_Date__c",
    passportIssuingCountry: "Issuing_Country__c",
  },
  visaApplication: {
    usVisa: "Visa_Required__c",
    visaNumber: "Visa_Number__c",
  },
  languageProficiency: {
    testName: ["Name", "TestProvider__c", "ProficiencyQualification__c"],
    testTakenDate: "TestDate__c",
    total: "TestScore__c",
    internetBasedTest: "TestScore__c",
    reading: "Reading_Score__c",
    writing: "Writing_Score__c",
    listening: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    overallBandScore: "TestScore__c",
  },
};

export const salesforceAgentConfig = {
  OpportunityFile__c: ["documents"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  Visa_Application__c: ["visaApplication"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    primaryPhoneNumber: "Phone",
    phoneNumber: "Phone",
    country: "Country__c",

    program: "Programme__c",
    location: "Location__c",

    // 🔹 Agent & Ownership
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",

    // 🔹 Business & Record Metadata
    brand: "Brand__c",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    leadRecordTypeId: "RecordTypeId",
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email__c",
    phone: "Phone",
    country: "PersonMailingCountry",
    reportYourSex: "Gender__c",
    countryOfBirth: "CountryOfBirth__c",
    stateOrProvinceOfBirth: "PreferedofBirth*__c",
    passportFirstName: "FirstName",
    passportLastName: "LastName",
    birthDate: "DateofBirth__c",
    placeOfBirth: "PlaceOfBirth__c",
    middleName: "Middle_Name__c",
    suffix: "LastName",
    formerLastName: "Former_Last_Name__c",
    myFirstName: "PreferedFirstName__c",
    countryOrTerritory: "PersonMailingCountry",
    streetAddress: "PersonMailingStreet",
    city: "PersonMailingCity",
    stateOrProvince: "PersonMailingState",
    county: "PersonMailingState",
    postalCode: "PersonMailingPostalCode",
    permanentCountryOrTerritory: "PersonMailingCountry",
    permanentStreetAddress: "PersonMailingStreet",
    permanentCity: "PersonMailingCity",
    permanentStateOrProvince: "PersonMailingState",
    permanentCounty: "PersonMailingState",
    permanentPostalCodeTwo: "PersonMailingPostalCode",
    primaryPhoneNumber: "Phone",
    alternatePhoneNumber: "PrimaryPhone__c",
    unitedStatesCitizenshipStatus: "Citizenship__c",
    countryOfCitizenship: "Citizenship__c",
    secondCountryOfCitizenship: "Dual_Citizenship__c",
    isHispanicOrLatino: "Ethnicity__c",
    firstLanguage: "Primary_Language__c",
    privacyPolicyConsent: [
      "HasOptedOutOfEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],

    // 🔹 Organizational/Business Info
    businessUnit: "BusinessUnit__c",
    brand: "Brand__c",
    accountRecordTypeId: "RecordTypeId",
    agentContactUserId: "OwnerId",

    // 🔹 Academic & Intake
    programTypeDisplayName: "Level__pc",
  },
  Opportunity: {
    usVisa: "Visa__c",
    typeOfVisa: "VisaType__c",
    issuedCountry: "VisaCountry__c",
    validFromDate: "VisaIssueDate__c",
    validUntilDate: "VisaExpiryDate__c",

    // 🔹 Program Details
    program: "Programme__c",
    programDisplayName: "Name",
    intake: ["CloseDate", "Product_Intake_Date__c", "OverallStartDate__c"],

    // 🔹 Agent & User Information
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",

    // 🔹 Record Metadata
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    pricebookId: "Pricebook2Id",
  },

  Application__c: {
    firstName: "First_Name__c",
    lastName: "Last_Name__c",
    email: "Email__c",
    phoneNumber: "Mobile__c",
    country: "Country__c",
    reportYourSex: "Gender__c",
    countryOfBirth: "CountryOfBirth__c",
    birthDate: "Date_of_birth__c",
    placeOfBirth: "Place_of_Birth__c",
    middleName: "Middle_Other_Name_s__c",
    suffix: "Last_Name__c",
    primaryPhoneNumber: "Mobile__c",
    alternatePhoneNumber: "Alternate_Phone_Number__c",

    // 🔹 Academic Program Details
    program: "Programme__c",
    programDisplayName: "Program_Of_Study__c",
    programType: "Level_Of_Study__c",
    intake: ["Start_Date__c", "Intake__c"],

    // 🔹 Agent & Ownership
    agentContactUserId: "OwnerId",
    accountManagerUserId: "Business_Developer__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",

    // 🔹 Application Metadata
    applicationId: "Application_Form_Id__c",
    applicationRecordTypeId: "RecordTypeId",
  },
};
